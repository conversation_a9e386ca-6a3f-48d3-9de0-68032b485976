"""
Barcode Detection Utilities
Core logic for detecting and decoding Code 39 barcodes
"""

import cv2
import numpy as np
from pyzbar import pyzbar
from datetime import datetime
import json


class BarcodeDetector:
    def __init__(self):
        self.detected_barcodes = []
        self.last_detection_time = None
        
    def detect_barcodes(self, frame):
        """
        Detect barcodes in a frame
        
        Args:
            frame: OpenCV image frame
            
        Returns:
            list: List of detected barcode objects
        """
        # Convert to grayscale for better detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Apply some preprocessing to improve detection
        # Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Detect barcodes
        barcodes = pyzbar.decode(blurred)
        
        return barcodes
    
    def process_barcode(self, barcode):
        """
        Process a detected barcode and extract information
        
        Args:
            barcode: pyzbar barcode object
            
        Returns:
            dict: Processed barcode information
        """
        # Extract barcode data
        barcode_data = barcode.data.decode('utf-8')
        barcode_type = barcode.type
        
        # Get bounding box coordinates
        (x, y, w, h) = barcode.rect
        
        # Create barcode info dictionary
        barcode_info = {
            'data': barcode_data,
            'type': barcode_type,
            'rect': (x, y, w, h),
            'polygon': barcode.polygon,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'confidence': self._calculate_confidence(barcode)
        }
        
        return barcode_info
    
    def _calculate_confidence(self, barcode):
        """
        Calculate confidence score for barcode detection
        
        Args:
            barcode: pyzbar barcode object
            
        Returns:
            float: Confidence score (0-100)
        """
        # Simple confidence calculation based on barcode quality
        # In a real implementation, you might use more sophisticated metrics
        
        if len(barcode.data) > 0:
            # Base confidence on data length and type
            base_confidence = min(90, len(barcode.data) * 10)
            
            # Bonus for Code 39 barcodes
            if barcode.type == 'CODE39':
                base_confidence += 10
                
            return min(100, base_confidence)
        
        return 0
    
    def draw_barcode_overlay(self, frame, barcode_info):
        """
        Draw barcode detection overlay on frame
        
        Args:
            frame: OpenCV image frame
            barcode_info: Processed barcode information
            
        Returns:
            frame: Frame with overlay drawn
        """
        x, y, w, h = barcode_info['rect']
        
        # Draw bounding rectangle
        cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # Draw barcode type and data
        text = f"{barcode_info['type']}: {barcode_info['data']}"
        cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 
                   0.6, (0, 255, 0), 2)
        
        # Draw confidence
        confidence_text = f"Confidence: {barcode_info['confidence']:.1f}%"
        cv2.putText(frame, confidence_text, (x, y + h + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
        
        # Draw timestamp
        time_text = f"Time: {barcode_info['timestamp']}"
        cv2.putText(frame, time_text, (x, y + h + 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        return frame
    
    def save_detection(self, barcode_info, filename='detected_barcodes.json'):
        """
        Save detected barcode to file
        
        Args:
            barcode_info: Processed barcode information
            filename: Output filename
        """
        self.detected_barcodes.append(barcode_info)
        
        try:
            with open(filename, 'w') as f:
                json.dump(self.detected_barcodes, f, indent=2)
        except Exception as e:
            print(f"Error saving detection: {e}")
    
    def is_code39(self, barcode_info):
        """
        Check if detected barcode is Code 39 format
        
        Args:
            barcode_info: Processed barcode information
            
        Returns:
            bool: True if Code 39, False otherwise
        """
        return barcode_info['type'] == 'CODE39'
    
    def validate_code39(self, data):
        """
        Validate Code 39 barcode data
        
        Args:
            data: Barcode data string
            
        Returns:
            bool: True if valid Code 39, False otherwise
        """
        # Code 39 valid characters
        valid_chars = set('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%*')
        
        # Check if all characters are valid
        return all(c in valid_chars for c in data.upper())
    
    def get_detection_stats(self):
        """
        Get detection statistics
        
        Returns:
            dict: Detection statistics
        """
        total_detections = len(self.detected_barcodes)
        code39_detections = sum(1 for b in self.detected_barcodes if b['type'] == 'CODE39')
        
        return {
            'total_detections': total_detections,
            'code39_detections': code39_detections,
            'other_detections': total_detections - code39_detections,
            'last_detection': self.detected_barcodes[-1] if self.detected_barcodes else None
        }
