<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Barcode Scanner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            width: 100%;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .camera-container {
            position: relative;
            background: #000;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #video {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: cover;
        }
        
        .overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 150px;
            border: 3px solid #00ff00;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
            pointer-events: none;
        }
        
        .overlay::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px dashed rgba(0, 255, 0, 0.3);
            border-radius: 15px;
        }
        
        .controls {
            padding: 20px;
            text-align: center;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .status.success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .result {
            background: #e7f3ff;
            padding: 20px;
            margin: 10px 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            display: none;
        }
        
        .result.show {
            display: block;
        }
        
        .result-type {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .result-data {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .result-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .supported-formats {
            padding: 20px;
            background: #f8f9fa;
            margin: 10px 20px;
            border-radius: 10px;
        }
        
        .supported-formats h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .format-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            font-size: 0.9rem;
        }
        
        .format-item {
            background: white;
            padding: 8px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .overlay {
                width: 200px;
                height: 120px;
            }
            
            .btn {
                padding: 12px 25px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Mobile Barcode Scanner</h1>
            <p>Universal barcode detection for all formats</p>
        </div>
        
        <div class="camera-container">
            <video id="video" autoplay playsinline></video>
            <div class="overlay"></div>
        </div>
        
        <div class="controls">
            <button id="startBtn" class="btn">📷 Start Camera</button>
            <button id="stopBtn" class="btn" disabled>⏹️ Stop Camera</button>
        </div>
        
        <div id="status" class="status">
            Click "Start Camera" to begin scanning
        </div>
        
        <div id="result" class="result">
            <div class="result-type" id="resultType"></div>
            <div class="result-data" id="resultData"></div>
            <div class="result-meta" id="resultMeta"></div>
        </div>
        
        <div class="supported-formats">
            <h3>📊 Supported Formats</h3>
            <div class="format-list">
                <div class="format-item">Code 39</div>
                <div class="format-item">Code 128</div>
                <div class="format-item">EAN-13</div>
                <div class="format-item">EAN-8</div>
                <div class="format-item">UPC-A</div>
                <div class="format-item">UPC-E</div>
                <div class="format-item">QR Code</div>
                <div class="format-item">DataMatrix</div>
                <div class="format-item">PDF417</div>
                <div class="format-item">Aztec</div>
                <div class="format-item">Codabar</div>
                <div class="format-item">ITF</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/@zxing/library@latest/umd/index.min.js"></script>
    <script>
        class MobileBarcodeScanner {
            constructor() {
                this.video = document.getElementById('video');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.status = document.getElementById('status');
                this.result = document.getElementById('result');
                this.resultType = document.getElementById('resultType');
                this.resultData = document.getElementById('resultData');
                this.resultMeta = document.getElementById('resultMeta');
                
                this.stream = null;
                this.codeReader = null;
                this.scanning = false;
                
                this.initializeScanner();
                this.setupEventListeners();
            }
            
            initializeScanner() {
                // Initialize ZXing code reader for multiple formats
                this.codeReader = new ZXing.BrowserMultiFormatReader();
                console.log('ZXing code reader initialized');
            }
            
            setupEventListeners() {
                this.startBtn.addEventListener('click', () => this.startScanning());
                this.stopBtn.addEventListener('click', () => this.stopScanning());
            }
            
            async startScanning() {
                try {
                    this.updateStatus('🔍 Starting camera...', 'info');
                    
                    // Get camera constraints for back camera
                    const constraints = {
                        video: {
                            facingMode: { ideal: 'environment' }, // Back camera
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    };
                    
                    // Start video stream
                    this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                    this.video.srcObject = this.stream;
                    
                    // Wait for video to be ready
                    await new Promise((resolve) => {
                        this.video.onloadedmetadata = resolve;
                    });
                    
                    // Start barcode detection
                    this.scanning = true;
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;
                    
                    this.updateStatus('📷 Camera active - Point at barcode', 'success');
                    
                    // Start continuous scanning
                    this.scanContinuously();
                    
                } catch (error) {
                    console.error('Error starting camera:', error);
                    this.updateStatus('❌ Camera access denied or not available', 'error');
                }
            }
            
            async scanContinuously() {
                if (!this.scanning) return;
                
                try {
                    const result = await this.codeReader.decodeOnceFromVideoDevice(undefined, this.video);
                    
                    if (result) {
                        this.handleBarcodeDetected(result);
                        
                        // Continue scanning after a short delay
                        setTimeout(() => {
                            if (this.scanning) {
                                this.scanContinuously();
                            }
                        }, 1000);
                    } else {
                        // No barcode found, try again
                        if (this.scanning) {
                            requestAnimationFrame(() => this.scanContinuously());
                        }
                    }
                } catch (error) {
                    // Continue scanning even if there's an error
                    if (this.scanning) {
                        setTimeout(() => this.scanContinuously(), 100);
                    }
                }
            }
            
            handleBarcodeDetected(result) {
                const barcodeData = result.text;
                const barcodeFormat = result.format;
                const timestamp = new Date().toLocaleString();
                
                console.log('Barcode detected:', { barcodeData, barcodeFormat });
                
                // Update UI
                this.resultType.textContent = `${barcodeFormat}`;
                this.resultData.textContent = barcodeData;
                this.resultMeta.textContent = `Detected at ${timestamp}`;
                this.result.classList.add('show');
                
                this.updateStatus(`✅ Detected: ${barcodeFormat}`, 'success');
                
                // Vibrate if supported
                if (navigator.vibrate) {
                    navigator.vibrate(200);
                }
                
                // Send to Python backend if available
                this.sendToPythonBackend(barcodeData, barcodeFormat);
            }
            
            async sendToPythonBackend(data, format) {
                try {
                    // Try to send to local Python server
                    const response = await fetch('http://localhost:8000/barcode', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            data: data,
                            format: format,
                            timestamp: new Date().toISOString()
                        })
                    });
                    
                    if (response.ok) {
                        console.log('Barcode sent to Python backend successfully');
                    }
                } catch (error) {
                    // Backend not available, that's okay
                    console.log('Python backend not available:', error.message);
                }
            }
            
            stopScanning() {
                this.scanning = false;
                
                if (this.codeReader) {
                    this.codeReader.reset();
                }
                
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }
                
                this.video.srcObject = null;
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                
                this.updateStatus('📷 Camera stopped', 'info');
                this.result.classList.remove('show');
            }
            
            updateStatus(message, type = 'info') {
                this.status.textContent = message;
                this.status.className = `status ${type}`;
            }
        }
        
        // Initialize scanner when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new MobileBarcodeScanner();
        });
    </script>
</body>
</html>
