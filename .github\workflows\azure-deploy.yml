name: Deploy to Azure

on:
  push:
    branches: [ master ]
  workflow_dispatch:

env:
  AZURE_RESOURCE_GROUP: attendoo-rg
  BACKEND_CONTAINER_NAME: attendoo-backend
  FRONTEND_CONTAINER_NAME: attendoo-frontend
  BACKEND_APP_NAME: attendoo-backend-app
  FRONTEND_APP_NAME: attendoo-frontend-app

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          kumarharsh001/attendoo:backend-latest
          kumarharsh001/attendoo:backend-${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          kumarharsh001/attendoo:frontend-latest
          kumarharsh001/attendoo:frontend-${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Deploy Backend to Azure Container Instances
      uses: azure/aci-deploy@v1
      with:
        resource-group: ${{ env.AZURE_RESOURCE_GROUP }}
        dns-name-label: attendoo-backend-${{ github.run_number }}
        image: kumarharsh001/attendoo:backend-${{ github.sha }}
        name: ${{ env.BACKEND_CONTAINER_NAME }}
        location: 'East US'
        environment-variables: |
          MONGODB_URI=${{ secrets.MONGODB_URI }}
          PORT=5000
          NODE_ENV=production
        ports: 5000
        cpu: 1
        memory: 1.5

    - name: Get Backend URL
      id: backend-url
      run: |
        BACKEND_FQDN=$(az container show \
          --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
          --name ${{ env.BACKEND_CONTAINER_NAME }} \
          --query ipAddress.fqdn \
          --output tsv)
        echo "backend_url=http://${BACKEND_FQDN}:5000" >> $GITHUB_OUTPUT
        echo "Backend URL: http://${BACKEND_FQDN}:5000"

    - name: Deploy Frontend to Azure Container Instances
      uses: azure/aci-deploy@v1
      with:
        resource-group: ${{ env.AZURE_RESOURCE_GROUP }}
        dns-name-label: attendoo-frontend-${{ github.run_number }}
        image: kumarharsh001/attendoo:frontend-${{ github.sha }}
        name: ${{ env.FRONTEND_CONTAINER_NAME }}
        location: 'East US'
        environment-variables: |
          REACT_APP_API_BASE_URL=${{ steps.backend-url.outputs.backend_url }}
        ports: 80
        cpu: 0.5
        memory: 1

    - name: Get Deployment URLs
      run: |
        BACKEND_FQDN=$(az container show \
          --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
          --name ${{ env.BACKEND_CONTAINER_NAME }} \
          --query ipAddress.fqdn \
          --output tsv)
        
        FRONTEND_FQDN=$(az container show \
          --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
          --name ${{ env.FRONTEND_CONTAINER_NAME }} \
          --query ipAddress.fqdn \
          --output tsv)
        
        echo "🚀 Deployment Complete!"
        echo "Frontend URL: http://${FRONTEND_FQDN}"
        echo "Backend URL: http://${BACKEND_FQDN}:5000"
        echo "Health Check: http://${BACKEND_FQDN}:5000/api/health"
        echo "Scanner: http://${FRONTEND_FQDN}/native-camera.html"
        
        # Save URLs to GitHub summary
        echo "## 🚀 Deployment URLs" >> $GITHUB_STEP_SUMMARY
        echo "- **Frontend**: http://${FRONTEND_FQDN}" >> $GITHUB_STEP_SUMMARY
        echo "- **Backend**: http://${BACKEND_FQDN}:5000" >> $GITHUB_STEP_SUMMARY
        echo "- **Health Check**: http://${BACKEND_FQDN}:5000/api/health" >> $GITHUB_STEP_SUMMARY
        echo "- **Scanner**: http://${FRONTEND_FQDN}/native-camera.html" >> $GITHUB_STEP_SUMMARY

    - name: Test Deployment
      run: |
        BACKEND_FQDN=$(az container show \
          --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
          --name ${{ env.BACKEND_CONTAINER_NAME }} \
          --query ipAddress.fqdn \
          --output tsv)
        
        echo "Testing backend health..."
        for i in {1..10}; do
          if curl -f "http://${BACKEND_FQDN}:5000/api/health"; then
            echo "✅ Backend is healthy!"
            break
          else
            echo "⏳ Waiting for backend to start... (attempt $i/10)"
            sleep 30
          fi
        done
        
        FRONTEND_FQDN=$(az container show \
          --resource-group ${{ env.AZURE_RESOURCE_GROUP }} \
          --name ${{ env.FRONTEND_CONTAINER_NAME }} \
          --query ipAddress.fqdn \
          --output tsv)
        
        echo "Testing frontend..."
        for i in {1..5}; do
          if curl -f "http://${FRONTEND_FQDN}"; then
            echo "✅ Frontend is accessible!"
            break
          else
            echo "⏳ Waiting for frontend to start... (attempt $i/5)"
            sleep 20
          fi
        done
