uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push backend Docker image
      uses: docker/build-push-action@v3
      with:
        context: ./backend
        file: ./backend/Dockerfile
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/attendance-backend:latest

  deploy:
    needs: build-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
=======
