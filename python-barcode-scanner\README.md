# Python Barcode Code 39 Scanner

A complete Python application for detecting and displaying Code 39 barcodes using computer vision.

## Features

- **Real-time barcode detection** using OpenCV
- **Code 39 barcode support** - industry standard format
- **Live camera feed** with barcode overlay
- **Decoded text display** with timestamp
- **Save detected barcodes** to file
- **Simple GUI interface** using tkinter
- **Cross-platform** - works on Windows, Mac, Linux

## Requirements

- Python 3.7+
- OpenCV (cv2)
- pyzbar
- tkinter (usually included with Python)
- PIL (Pillow)

## Installation

1. **Clone or download this folder**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the scanner:**
   ```bash
   python barcode_scanner.py
   ```

## Usage

### Command Line Scanner
```bash
python barcode_scanner.py
```

### GUI Scanner
```bash
python gui_scanner.py
```

### Batch Image Processing
```bash
python batch_scanner.py --input images/ --output results.txt
```

## File Structure

```
python-barcode-scanner/
├── README.md
├── requirements.txt
├── barcode_scanner.py      # Main CLI scanner
├── gui_scanner.py          # GUI version
├── batch_scanner.py        # Batch processing
├── barcode_generator.py    # Generate test barcodes
├── utils/
│   ├── __init__.py
│   ├── detector.py         # Core detection logic
│   └── display.py          # Display utilities
├── test_images/            # Sample barcode images
└── output/                 # Saved results
```

## Code 39 Barcode Format

Code 39 is a variable-length, discrete barcode symbology that can encode:
- Numbers (0-9)
- Letters (A-Z)
- Special characters (-, ., $, /, +, %, space)
- Start/stop character (*)

Example: `*HELLO123*`

## Output

The scanner will display:
- Live camera feed with detected barcodes highlighted
- Decoded barcode text
- Timestamp of detection
- Confidence level
- Barcode type and format

## Troubleshooting

### Camera Issues
- Check camera permissions
- Try different camera indices (0, 1, 2...)
- Ensure good lighting conditions

### Detection Issues
- Ensure barcode is Code 39 format
- Check barcode quality and size
- Adjust camera distance and angle
- Verify barcode is not damaged

### Performance Issues
- Reduce camera resolution
- Adjust detection parameters
- Close other camera applications
