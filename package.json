{"name": "attendance-system", "version": "1.0.0", "description": "A comprehensive attendance management system with Docker containerization", "main": "index.js", "scripts": {"docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:clean": "docker-compose down -v --rmi all", "docker:dev": "docker-compose up --build", "sonar": "sonarqube-scanner", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["attendance", "management", "system", "react", "nodejs", "mongodb", "docker", "devops"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "sonarqube-scanner": "^4.3.0"}, "dependencies": {"multer": "^2.0.0"}}