#!/bin/bash

# Azure Setup Script for Attendoo Application
echo "🚀 Setting up Azure resources for Attendoo..."

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if logged in
if ! az account show &> /dev/null; then
    echo "❌ Not logged into Azure. Please run 'az login' first."
    exit 1
fi

# Get subscription ID
SUBSCRIPTION_ID=$(az account show --query id --output tsv)
echo "📋 Using subscription: $SUBSCRIPTION_ID"

# Create resource group
echo "📦 Creating resource group..."
az group create --name attendoo-rg --location "East US"

# Create service principal for GitHub Actions
echo "🔐 Creating service principal for GitHub Actions..."
CREDENTIALS=$(az ad sp create-for-rbac \
    --name "github-actions-attendoo" \
    --role contributor \
    --scopes /subscriptions/$SUBSCRIPTION_ID \
    --sdk-auth)

echo ""
echo "✅ Azure setup complete!"
echo ""
echo "🔧 Next steps:"
echo "1. Add this to GitHub Secrets as 'AZURE_CREDENTIALS':"
echo "$CREDENTIALS"
echo ""
echo "2. Add this to GitHub Secrets as 'MONGODB_URI':"
echo "mongodb+srv://harshkumar170604:<EMAIL>/attend?retryWrites=true&w=majority&appName=Cluster0"
echo ""
echo "3. Push your code to trigger deployment!"
echo ""
echo "📊 Resource Group: attendoo-rg"
echo "🌍 Location: East US"
echo "🔑 Service Principal: github-actions-attendoo"
