:root {
  --primary-color: #2563eb;
  --secondary-color: #1e40af;
  --background-color: #f8fafc;
  --text-color: #1e293b;
  --border-color: #e2e8f0;
}

.app {
  min-height: 100vh;
  background-color: var(--background-color);
}

.main-content {
  padding: 2rem 0;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

/* Button Styles */
.btn-primary {
  background-color: var(--primary-color);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
}

/* Navigation Styles */
.navbar {
  background-color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: var(--background-color);
}

.nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

/* Form Styles */
.form-control {
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Table Styles */
.table {
  background-color: white;
  border-radius: 1rem;
  overflow: hidden;
}

.table thead th {
  background-color: var(--background-color);
  border-bottom: 2px solid var(--border-color);
  color: var(--text-color);
  font-weight: 600;
}

/* Scanner Styles */
.scanner-container {
  background-color: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* Status Badge Styles */
.badge {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
}

.badge-present {
  background-color: #dcfce7;
  color: #166534;
}

.badge-absent {
  background-color: #fee2e2;
  color: #991b1b;
} 