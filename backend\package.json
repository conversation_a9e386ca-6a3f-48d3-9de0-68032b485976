{"name": "attendance-system-backend", "version": "1.0.0", "description": "Backend for attendance system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "create-students": "node scripts/createStudents.js", "create-attendance": "node scripts/createAttendance.js", "test-connection": "node scripts/testConnection.js", "update-dates": "node scripts/updateDates.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "test-workflow": "node scripts/test-workflow.js", "test-attendance": "node scripts/test-attendance.js", "test": "npm run test-workflow && npm run test-attendance"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jimp": "^1.6.0", "mongoose": "^7.5.0", "multer": "^2.0.0", "prom-client": "^15.1.3", "qrcode-reader": "^1.0.4"}, "devDependencies": {"eslint": "^8.56.0", "nodemon": "^3.0.1"}}