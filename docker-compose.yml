version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    container_name: grafana
    ports:
      - "3002:3000"
  
  # Backend Service
  backend:
    build:
      context: ./backend
    image: kumarharsh001/attendoo:backend
    container_name: attendo-backend
    restart: always
    environment:
      - MONGODB_URI=mongodb+srv://harshkumar170604:<EMAIL>/attend?retryWrites=true&w=majority&appName=Cluster0
      - PORT=5000
    ports:
      - "5000:5000"

  # Frontend Service
  frontend:
    build:
      context: ./frontend
    image: kumarharsh001/attendoo:frontend
    container_name: attendo-frontend
    restart: always
    ports:
      - "3000:80"
    depends_on:
      - backend

# volumes:
  # mongo-data: # No longer needed since using MongoDB Atlas
