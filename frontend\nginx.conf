# Define upstream with resolver for dynamic hostname resolution
upstream backend_upstream {
    # Use Docker's internal DNS resolver
    server backend:5000 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Set resolver for dynamic DNS resolution (<PERSON><PERSON>'s internal DNS)
    resolver 127.0.0.11 valid=30s;

    # Handle React Router (SPA)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy to backend with error handling
    location /api/ {
        # Try to proxy to backend, fallback to error page if backend is down
        error_page 502 503 504 = @backend_fallback;

        # Set variable for dynamic resolution
        set $backend_url "http://backend:5000";

        proxy_pass $backend_url;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Timeout settings
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }

    # Fallback when backend is unavailable
    location @backend_fallback {
        add_header Content-Type application/json;
        return 503 '{"error": "Backend service is currently unavailable. Please try again later.", "status": 503, "timestamp": "$time_iso8601"}';
    }

    # Health check endpoint for frontend
    location /health {
        add_header Content-Type application/json;
        return 200 '{"status": "healthy", "service": "frontend", "timestamp": "$time_iso8601"}';
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
