<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Native Camera Barcode Scanner</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    #interactive {
      width: 100%;
      max-width: 400px;
      border: 1px solid #ccc;
      margin-bottom: 10px;
      position: relative;
    }
    #interactive video {
      width: 100%;
    }
    #barcodeValue {
      font-weight: bold;
      font-size: 1.2em;
      color: green;
    }
    #startBtn, #stopBtn {
      margin-right: 10px;
      padding: 8px 16px;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <h2>Scan your Barcode</h2>
  <div id="interactive" class="viewport"></div>
  <div>
    <button id="startBtn">Start Scanning</button>
    <button id="stopBtn" disabled>Stop Scanning</button>
  </div>
  <p id="status">Click "Start Scanning" to begin</p>
  <p>Detected Barcode: <span id="barcodeValue">None</span></p>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>
  <script>
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const status = document.getElementById('status');
    const barcodeValue = document.getElementById('barcodeValue');

    function logStatus(message) {
      console.log(message);
      status.textContent = message;
    }

    function startScanner() {
      if (Quagga.initialized) {
        Quagga.start();
        logStatus('Scanning started');
        startBtn.disabled = true;
        stopBtn.disabled = false;
        return;
      }

      Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: document.querySelector('#interactive'),
          constraints: {
            facingMode: "environment" // or user for front camera
          },
        },
        decoder: {
          readers: [
            "code_128_reader",
            "ean_reader",
            "ean_8_reader",
            "code_39_reader",
            "code_39_vin_reader",
            "codabar_reader",
            "upc_reader",
            "upc_e_reader",
            "i2of5_reader"
          ]
        },
        locate: true
      }, function(err) {
        if (err) {
          logStatus('Error initializing Quagga: ' + err);
          return;
        }
        Quagga.initialized = true;
        Quagga.start();
        logStatus('Scanning started');
        startBtn.disabled = true;
        stopBtn.disabled = false;
      });

      Quagga.onDetected(function(result) {
        if (result && result.codeResult && result.codeResult.code) {
          const code = result.codeResult.code;
          barcodeValue.textContent = code;
          logStatus('Barcode detected: ' + code);
          // Send to backend here:
          fetch('http://localhost:5000/api/scan', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ scanCode: code }),
          })
          .then(res => {
            if (!res.ok) {
              throw new Error('Network response was not ok');
            }
            return res.json();
          })
          .then(data => {
            console.log('Backend response:', data);
            logStatus('Backend response: ' + data.message);
          })
          .catch(err => {
            console.error('Error sending scan code:', err);
            logStatus('Error sending scan code: ' + err.message);
          });
        }
      });
    }

    function stopScanner() {
      Quagga.stop();
      logStatus('Scanning stopped');
      startBtn.disabled = false;
      stopBtn.disabled = true;
      barcodeValue.textContent = 'None';
    }

    startBtn.addEventListener('click', startScanner);
    stopBtn.addEventListener('click', stopScanner);
  </script>
</body>
</html>
